import React, { useMemo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import {
  useTable,
  usePagination,
  useGlobalFilter,
  useAsyncDebounce,
  useSortBy,
  useFilters,
} from "react-table";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Icon from "@mui/material/Icon";
import Switch from "@mui/material/Switch";
import Autocomplete from "@mui/material/Autocomplete";
import MDBox from "components/atoms/MDBox";
import MDTypography from "components/atoms/MDTypography";
import MDInput from "components/atoms/MDInput";
import MDPagination from "components/atoms/MDPagination";
import DataTableHeadCell from "examples/Tables/DataTable/DataTableHeadCell";
import DataTableBodyCell from "examples/Tables/DataTable/DataTableBodyCell";
import { ColumnWiseFilter } from "utils/helperFunctions/columnWiseFilter";
import { ActionButtons } from "utils/helperFunctions/actionButtons";
import { DefaultColumnFilter } from "utils/helperFunctions/columnWiseFilter";

function DataTable({
  entriesPerPage= { defaultValue: 10, entries: ["5", "10", "15", "20", "25"] },
  canSearch=false,
  showTotalEntries=false,
  table,
  pagination={ variant: "gradient", color: "info" },
  isSorted=false,
  noEndBorder=false,
  editData=false,
  deleteData=false,
  viewData=false,
  grades=false,
  uploadSubmission=false,
  enableFilter=false,
  viewSubmission=false,
  onEdit = () => {},
  onDelete = () => {},
  onView = () => {},
  onGrade = () => {},
  onUploadSubmission = () => {},
  onViewSubmission = () => {},
  isRowClickable,
  handleRowClick,
  isCustomActionButton,
  customEntriesPerPage,
  publish = false,
  onPublish = () => {},
  publishingStudentId = null,
  isActionButtonCentered = true
}) {
  const defaultValue = customEntriesPerPage 
    ? customEntriesPerPage 
    : entriesPerPage.defaultValue 
      ? entriesPerPage.defaultValue 
      : 10;

  const entries = entriesPerPage.entries
    ? entriesPerPage.entries.map((el) => el.toString())
    : ["5", "10", "15", "20", "25"];
  const columns = useMemo(() => {
    return table.columns.map((column) => {
      if (column.accessor === 'sectionList') {
        return {
          ...column,
          Cell: ({ value }) => value.join(', '),
        };
      }
      return column;
    });
  }, [table]);
    
  const data = useMemo(() => table.rows, [table]);
  const [isFilterEnabled, setIsFilterEnabled] = useState(false);
  const defaultColumn = useMemo(
    () => ({
      Filter: ColumnWiseFilter,
      filter: DefaultColumnFilter,
    }),
    []
  );


  const tableInstance = useTable(
    { columns, data, defaultColumn, initialState: { pageIndex: 0 } },
    useFilters,
    useGlobalFilter,
    useSortBy,
    usePagination
  );

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    rows,
    page,
    pageOptions,
    canPreviousPage,
    canNextPage,
    gotoPage,
    nextPage,
    previousPage,
    setPageSize,
    setGlobalFilter,
    setAllFilters,
    state: { pageIndex, pageSize, globalFilter },
  } = tableInstance;

  useEffect(() => setPageSize(defaultValue || 10), [defaultValue]);

  const setEntriesPerPage = (value) => setPageSize(value);

  const handleFilterToggle = (event) => {
    const isEnabled = event.target.checked;
    setIsFilterEnabled(isEnabled);
    if (!isEnabled) {
      setAllFilters([]);
    }
  };

  const renderPagination = (() => {
    const totalPages = pageOptions.length;
    const maxPagesToShow = 5;
  
    const start = Math.max(
      Math.min(pageIndex - Math.floor(maxPagesToShow / 2), totalPages - maxPagesToShow),
      0
    );
    const end = Math.min(start + maxPagesToShow, totalPages);
  
    return pageOptions.slice(start, end).map((option) => (
      <MDPagination
        item
        key={option}
        onClick={() => gotoPage(Number(option))}
        active={pageIndex === option}
      >
        {option + 1}
      </MDPagination>
    ));
  })();

  const shouldDisplayInput = pageOptions.length > 6;
  
  const handleInputPagination = ({ target: { value } }) =>
    value > pageOptions.length || value < 0
      ? gotoPage(0)
      : gotoPage(Number(value));

  const customizedPageOptions = pageOptions.map((option) => option + 1);

  const handleInputPaginationValue = ({ target: value }) =>
    gotoPage(Number(value.value - 1));

  const [search, setSearch] = useState(globalFilter);

  const onSearchChange = useAsyncDebounce((value) => {
    setGlobalFilter(value || undefined);
  }, 100);

  const setSortedValue = (column) => {
    let sortedValue;

    if (isSorted && column.isSorted) {
      sortedValue = column.isSortedDesc ? "desc" : "asce";
    } else if (isSorted) {
      sortedValue = "none";
    } else {
      sortedValue = false;
    }

    return sortedValue;
  };

  const entriesStart =
    pageIndex === 0 ? pageIndex + 1 : pageIndex * pageSize + 1;

  let entriesEnd;

  if (pageIndex === 0) {
    entriesEnd = pageSize;
  } else if (pageIndex === pageOptions.length - 1) {
    entriesEnd = rows.length;
  } else {
    entriesEnd = pageSize * (pageIndex + 1);
  }

  return (
    <>
     {entriesPerPage || (canSearch && !enableFilter) ? (
      <MDBox
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        px={3}
        py={2}
        sx={{flexWrap: 'wrap'}}
      >
        {entriesPerPage && (
          <MDBox display="flex" alignItems="center">
            <Autocomplete
              autoSelect
              autoHighlight
              disableClearable
              value={pageSize.toString()}
              options={entries}
              onChange={(event, newValue) => {
                setEntriesPerPage(parseInt(newValue, 10));
              }}
              size="small"
              sx={{ width: "5rem" }}
              renderInput={(params) => <MDInput {...params} />}
            />
            <MDTypography variant="caption" color="secondary">
              &nbsp;&nbsp;entries per page
            </MDTypography>
          </MDBox>
        )}
        {canSearch && !enableFilter && (
          <MDBox width="12rem" ml="auto" mt={{ xs: 2, sm: 0 }}>
            <MDInput
              placeholder="Search..."
              value={search}
              size="small"
              fullWidth
              onChange={({ currentTarget }) => {
                setSearch(search);
                onSearchChange(currentTarget.value);
              }}
            />
          </MDBox>
        )}
        {enableFilter && !canSearch && (
          <MDBox sx={{marginTop: '20px'}}>
            <MDTypography fontWeight="bold" sx={{fontSize:'15px'}} alignItems="right">
              Filter By Columns
              <Switch
                checked={isFilterEnabled}
                onChange={handleFilterToggle}
                inputProps={{ "aria-label": "controlled" }}
              />
            </MDTypography>
          </MDBox>
        )}
      </MDBox>
    ) : null}
    <TableContainer sx={{ boxShadow: "none" }}>
      <Table {...getTableProps()}>
        <MDBox component="thead">
          {headerGroups.map((headerGroup, key) => (
            <TableRow key={key} {...headerGroup.getHeaderGroupProps()} sx={{verticalAlign: 'bottom'}}>
              {headerGroup.headers.map((column, idx) => {
                const { key, ...headerProps } = column.getHeaderProps(column.getSortByToggleProps());
                
                // for rendering % symbol in class dashboard table header
                const isShowPercentage = column?.Header === "Overall Performance" || column?.Header === "Highest Score"; 

                return(
                  <DataTableHeadCell
                  key={key || idx}
                  {...headerProps}
                  width={column.width ? column.width : "auto"}
                  align={column.align ? column.align : "left"}
                  sorted={setSortedValue(column)}
                  isVisible={column.display === true}
                  isShowPercentage={isShowPercentage}
                >
                  {isFilterEnabled && column.canFilter && (
                    <div>{column.render("Filter")}</div>
                  )}
                  {column.render("Header")}
                </DataTableHeadCell>
                )
              })}
              {(editData ||
                deleteData ||
                viewData ||
                grades ||
                uploadSubmission) && (
                <DataTableHeadCell align="center" isVisible={true}>
                  Action
                </DataTableHeadCell>
              )}
            </TableRow>
          ))}
        </MDBox>
        <TableBody {...getTableBodyProps()}>
          {page.map((row, key) => {
            prepareRow(row);
            return (
              <TableRow 
                key={key} 
                {...row.getRowProps()} 
                sx={{
                  height:'50px',
                  "&:hover": {
                    backgroundColor: isRowClickable ? "#f1f2f6" : "",
                    cursor: isRowClickable ? "pointer" : "default",
                  }
                }}
                onClick={() => isRowClickable && handleRowClick(row.values)}
                >
                {row.cells.map((cell, idx) => {
                  const { key, ...bodyProps } = cell.getCellProps();

                  // for rendering email as lowercase
                  const isEmailColumn = cell?.column?.id === 'email';

                  const formatValue = (value) => {
                    const isInt = (n) => parseInt(n) === n;
                  
                    if (typeof value === "number") {
                      return isInt(value) ? value : value.toFixed(2);
                    } else if (Array.isArray(value)) {
                      return value.join(", ");
                    }
                    return value;
                  };

                  return (
                    <DataTableBodyCell
                      key={key || idx}
                      noBorder={noEndBorder && rows.length - 1 === key}
                      align={cell.column.align ? cell.column.align : "left"}
                      isVisible={cell.column.display === true}
                      isEmailColumn={isEmailColumn}
                      {...bodyProps}
                    >
                      {cell.render("Cell").props.value &&
                      cell.render("Cell").props.column.key !== "score"
                        ? formatValue(cell.render("Cell").props.value)
                        : "0"}
                    </DataTableBodyCell>
                  )
                })}
                
                <ActionButtons
                  row={row}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onView={onView}
                  onGrade={onGrade}
                  onUploadSubmission={onUploadSubmission}
                  onViewSubmission={onViewSubmission}
                  editData={editData}
                  deleteData={deleteData}
                  viewData={viewData}
                  grades={grades}
                  uploadSubmission={uploadSubmission}
                  viewSubmission={viewSubmission}
                  isCustomActionButton={isCustomActionButton}
                  publish={publish}
                  onPublish={onPublish}
                  publishingStudentId={publishingStudentId}
                  isActionButtonCentered={isActionButtonCentered}
                />
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      <MDBox
        display="flex"
        flexDirection={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "flex-start", sm: "center" }}
        p={!showTotalEntries && pageOptions.length === 1 ? 0 : 3}
      >
        {showTotalEntries && (
          <MDBox mb={{ xs: 3, sm: 0 }}>
            <MDTypography
              variant="button"
              color="secondary"
              fontWeight="regular"
            >
              Showing {entriesStart} to {entriesEnd} of {rows.length} entries
            </MDTypography>
          </MDBox>
        )}
         {pageOptions.length > 1 && (
            <MDPagination
              variant={pagination.variant ? pagination.variant : "gradient"}
              color={pagination.color ? pagination.color : "info"}
            >
              {shouldDisplayInput && (
                <MDBox width="5rem" ml={1}>
                  <MDInput
                    inputProps={{
                      type: "number",
                      min: 1,
                      max: customizedPageOptions.length,
                    }}
                    value={customizedPageOptions[pageIndex]}
                    onChange={
                      (handleInputPagination, handleInputPaginationValue)
                    }
                  />
                </MDBox>
              )}
              {canPreviousPage && (
                <MDPagination item onClick={() => previousPage()}>
                  <Icon sx={{ fontWeight: "bold" }}>chevron_left</Icon>
                </MDPagination>
              )}
              {renderPagination}
              {canNextPage && (
                <MDPagination item onClick={() => nextPage()}>
                  <Icon sx={{ fontWeight: "bold" }}>chevron_right</Icon>
                </MDPagination>
              )}
            </MDPagination>
          )}
      </MDBox>
    </TableContainer>
    </>
  );
}

DataTable.propTypes = {
  entriesPerPage: PropTypes.shape({
    defaultValue: PropTypes.number,
    entries: PropTypes.arrayOf(PropTypes.string),
  }),
  canSearch: PropTypes.bool,
  showTotalEntries: PropTypes.bool,
  pagination: PropTypes.shape({
    variant: PropTypes.string,
    color: PropTypes.string,
  }),
  table: PropTypes.shape({
    columns: PropTypes.arrayOf(PropTypes.object).isRequired,
    rows: PropTypes.arrayOf(PropTypes.object).isRequired,
  }).isRequired,
  isSorted: PropTypes.bool,
  noEndBorder: PropTypes.bool,
  filterByColumns: PropTypes.bool,
  enableFilter: PropTypes.bool,
  editData: PropTypes.bool,
  deleteData: PropTypes.bool,
  viewData: PropTypes.bool,
  grades: PropTypes.bool,
  uploadSubmission: PropTypes.bool,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onView: PropTypes.func,
  onGrade: PropTypes.func,
  onUploadSubmission: PropTypes.func,
  isEmailColumn: PropTypes.bool,
};

export default DataTable;
